// Basic Flutter widget test for CultureTrack app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:culture_track/main.dart';

void main() {
  testWidgets('CultureTrack app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const CultureTrackApp());

    // Verify that the app loads with the dashboard content
    expect(find.text('Welcome to CultureTrack'), findsOneWidget);

    // Verify bottom navigation is present
    expect(find.byType(BottomNavigationBar), findsOneWidget);
    expect(find.text('Cultures'), findsOneWidget);
    expect(find.text('Recipes'), findsOneWidget);

    // Test navigation to Cultures tab
    await tester.tap(find.text('Cultures'));
    await tester.pump();

    // Verify we're on the cultures screen
    expect(find.text('No cultures yet'), findsOneWidget);
  });
}
