import 'dart:convert';

/// Represents a tissue culture in the system
/// Maps to the cultures table in the database
class Culture {
  final int? id;
  final String species;
  final String? variety;
  final String? source;
  final DateTime creationDate;
  final CultureStage currentStage;
  final CultureStatus status;
  final DateTime? lastTransferDate;
  final DateTime? nextTransferDate;
  final String? notes;
  final List<String> photos;
  final List<ContaminationEvent> contaminationLog;
  final DateTime createdAt;
  final DateTime updatedAt;

  Culture({
    this.id,
    required this.species,
    this.variety,
    this.source,
    required this.creationDate,
    required this.currentStage,
    required this.status,
    this.lastTransferDate,
    this.nextTransferDate,
    this.notes,
    this.photos = const [],
    this.contaminationLog = const [],
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Create Culture from database map
  factory Culture.fromMap(Map<String, dynamic> map) {
    return Culture(
      id: map['id'] as int?,
      species: map['species'] as String,
      variety: map['variety'] as String?,
      source: map['source'] as String?,
      creationDate: DateTime.fromMillisecondsSinceEpoch(
        (map['creation_date'] as int) * 1000,
      ),
      currentStage: CultureStage.fromString(map['current_stage'] as String),
      status: CultureStatus.fromString(map['status'] as String),
      lastTransferDate:
          map['last_transfer_date'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                (map['last_transfer_date'] as int) * 1000,
              )
              : null,
      nextTransferDate:
          map['next_transfer_date'] != null
              ? DateTime.fromMillisecondsSinceEpoch(
                (map['next_transfer_date'] as int) * 1000,
              )
              : null,
      notes: map['notes'] as String?,
      photos: _parsePhotos(map['photos'] as String?),
      contaminationLog: _parseContaminationLog(
        map['contamination_log'] as String?,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        (map['created_at'] as int) * 1000,
      ),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(
        (map['updated_at'] as int) * 1000,
      ),
    );
  }

  /// Convert Culture to database map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'species': species,
      'variety': variety,
      'source': source,
      'creation_date': creationDate.millisecondsSinceEpoch ~/ 1000,
      'current_stage': currentStage.value,
      'status': status.value,
      'last_transfer_date':
          lastTransferDate != null
              ? lastTransferDate!.millisecondsSinceEpoch ~/ 1000
              : null,
      'next_transfer_date':
          nextTransferDate != null
              ? nextTransferDate!.millisecondsSinceEpoch ~/ 1000
              : null,
      'notes': notes,
      'photos': _encodePhotos(photos),
      'contamination_log': _encodeContaminationLog(contaminationLog),
      'created_at': createdAt.millisecondsSinceEpoch ~/ 1000,
      'updated_at': updatedAt.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// Create a copy of Culture with updated fields
  Culture copyWith({
    int? id,
    String? species,
    String? variety,
    String? source,
    DateTime? creationDate,
    CultureStage? currentStage,
    CultureStatus? status,
    DateTime? lastTransferDate,
    DateTime? nextTransferDate,
    String? notes,
    List<String>? photos,
    List<ContaminationEvent>? contaminationLog,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Culture(
      id: id ?? this.id,
      species: species ?? this.species,
      variety: variety ?? this.variety,
      source: source ?? this.source,
      creationDate: creationDate ?? this.creationDate,
      currentStage: currentStage ?? this.currentStage,
      status: status ?? this.status,
      lastTransferDate: lastTransferDate ?? this.lastTransferDate,
      nextTransferDate: nextTransferDate ?? this.nextTransferDate,
      notes: notes ?? this.notes,
      photos: photos ?? this.photos,
      contaminationLog: contaminationLog ?? this.contaminationLog,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Parse photos JSON string to `List<String>`
  static List<String> _parsePhotos(String? photosJson) {
    if (photosJson == null || photosJson.isEmpty) return [];
    try {
      final List<dynamic> photosList = jsonDecode(photosJson);
      return photosList.cast<String>();
    } catch (e) {
      return [];
    }
  }

  /// Encode photos `List<String>` to JSON string
  static String? _encodePhotos(List<String> photos) {
    if (photos.isEmpty) return null;
    return jsonEncode(photos);
  }

  /// Parse contamination log JSON string to `List<ContaminationEvent>`
  static List<ContaminationEvent> _parseContaminationLog(String? logJson) {
    if (logJson == null || logJson.isEmpty) return [];
    try {
      final List<dynamic> logList = jsonDecode(logJson);
      return logList
          .map(
            (item) => ContaminationEvent.fromMap(item as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Encode contamination log to JSON string
  static String? _encodeContaminationLog(List<ContaminationEvent> log) {
    if (log.isEmpty) return null;
    return jsonEncode(log.map((event) => event.toMap()).toList());
  }

  @override
  String toString() {
    return 'Culture(id: $id, species: $species, variety: $variety, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Culture && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Culture stage enumeration
enum CultureStage {
  initiation('initiation'),
  multiplication('multiplication'),
  rooting('rooting'),
  acclimatization('acclimatization');

  const CultureStage(this.value);
  final String value;

  static CultureStage fromString(String value) {
    return CultureStage.values.firstWhere(
      (stage) => stage.value == value,
      orElse: () => CultureStage.initiation,
    );
  }

  String get displayName {
    switch (this) {
      case CultureStage.initiation:
        return 'Initiation';
      case CultureStage.multiplication:
        return 'Multiplication';
      case CultureStage.rooting:
        return 'Rooting';
      case CultureStage.acclimatization:
        return 'Acclimatization';
    }
  }
}

/// Culture status enumeration
enum CultureStatus {
  active('active'),
  contaminated('contaminated'),
  transferred('transferred'),
  completed('completed'),
  failed('failed');

  const CultureStatus(this.value);
  final String value;

  static CultureStatus fromString(String value) {
    return CultureStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => CultureStatus.active,
    );
  }

  String get displayName {
    switch (this) {
      case CultureStatus.active:
        return 'Active';
      case CultureStatus.contaminated:
        return 'Contaminated';
      case CultureStatus.transferred:
        return 'Transferred';
      case CultureStatus.completed:
        return 'Completed';
      case CultureStatus.failed:
        return 'Failed';
    }
  }
}

/// Represents a contamination event in the culture log
class ContaminationEvent {
  final DateTime date;
  final String cause;
  final String? notes;

  ContaminationEvent({required this.date, required this.cause, this.notes});

  factory ContaminationEvent.fromMap(Map<String, dynamic> map) {
    return ContaminationEvent(
      date: DateTime.fromMillisecondsSinceEpoch((map['date'] as int) * 1000),
      cause: map['cause'] as String,
      notes: map['notes'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date.millisecondsSinceEpoch ~/ 1000,
      'cause': cause,
      'notes': notes,
    };
  }
}
