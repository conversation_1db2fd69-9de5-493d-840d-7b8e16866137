import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/culture_provider.dart';
import '../providers/recipe_provider.dart';

/// Demo widget to show Provider state management is working
/// This can be used for testing and demonstration purposes
class ProviderDemoWidget extends StatelessWidget {
  const ProviderDemoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Provider State Management Demo',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Culture Provider Status
            Consumer<CultureProvider>(
              builder: (context, cultureProvider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Culture Provider Status:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Total Cultures: ${cultureProvider.cultures.length}'),
                    Text('Active Cultures: ${cultureProvider.activeCulturesCount}'),
                    Text('Loading: ${cultureProvider.isLoading}'),
                    if (cultureProvider.error != null)
                      Text(
                        'Error: ${cultureProvider.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                  ],
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Recipe Provider Status
            Consumer<RecipeProvider>(
              builder: (context, recipeProvider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Recipe Provider Status:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Total Recipes: ${recipeProvider.recipes.length}'),
                    Text('Templates: ${recipeProvider.templatesCount}'),
                    Text('User Recipes: ${recipeProvider.userRecipes.length}'),
                    Text('Loading: ${recipeProvider.isLoading}'),
                    if (recipeProvider.error != null)
                      Text(
                        'Error: ${recipeProvider.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                  ],
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    context.read<CultureProvider>().loadCultures();
                  },
                  child: const Text('Load Cultures'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    context.read<RecipeProvider>().loadRecipes();
                  },
                  child: const Text('Load Recipes'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
