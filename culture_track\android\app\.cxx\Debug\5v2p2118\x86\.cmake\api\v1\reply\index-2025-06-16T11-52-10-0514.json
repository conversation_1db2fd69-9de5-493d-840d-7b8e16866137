{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/src/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/src/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/src/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/src/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-50003b38cca301e68f93.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-06d816cae9ff51e3a22f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-eaf6605f8bbf732da3b2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-06d816cae9ff51e3a22f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-eaf6605f8bbf732da3b2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-50003b38cca301e68f93.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}