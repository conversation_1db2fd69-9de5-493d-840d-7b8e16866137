import 'package:flutter/material.dart';
import '../models/culture.dart';

/// Widget for displaying individual culture items in a list
/// Shows key information and provides visual status indicators
class CultureListItem extends StatelessWidget {
  final Culture culture;
  final VoidCallback? onTap;

  const CultureListItem({super.key, required this.culture, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16.0),
        leading: _buildStatusIcon(),
        title: _buildTitle(),
        subtitle: _buildSubtitle(),
        trailing: _buildTrailing(),
        onTap: onTap,
      ),
    );
  }

  /// Build status icon with color coding
  Widget _buildStatusIcon() {
    IconData iconData;
    Color iconColor;

    switch (culture.status) {
      case CultureStatus.active:
        iconData = Icons.science;
        iconColor = Colors.green;
        break;
      case CultureStatus.contaminated:
        iconData = Icons.warning;
        iconColor = Colors.red;
        break;
      case CultureStatus.transferred:
        iconData = Icons.arrow_forward;
        iconColor = Colors.blue;
        break;
      case CultureStatus.completed:
        iconData = Icons.check_circle;
        iconColor = Colors.green.shade700;
        break;
      case CultureStatus.failed:
        iconData = Icons.error;
        iconColor = Colors.red.shade700;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withValues(alpha: 0.1),
      child: Icon(iconData, color: iconColor, size: 20),
    );
  }

  /// Build title with species and variety
  Widget _buildTitle() {
    final titleText =
        culture.variety != null
            ? '${culture.species} - ${culture.variety}'
            : culture.species;

    return Text(
      titleText,
      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// Build subtitle with stage and status information
  Widget _buildSubtitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 4),
        Row(
          children: [
            _buildStageChip(),
            const SizedBox(width: 8),
            _buildStatusChip(),
          ],
        ),
        const SizedBox(height: 4),
        _buildDateInfo(),
      ],
    );
  }

  /// Build stage chip
  Widget _buildStageChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Text(
        culture.currentStage.displayName,
        style: const TextStyle(
          color: Colors.blue,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build status chip
  Widget _buildStatusChip() {
    Color chipColor;
    switch (culture.status) {
      case CultureStatus.active:
        chipColor = Colors.green;
        break;
      case CultureStatus.contaminated:
        chipColor = Colors.red;
        break;
      case CultureStatus.transferred:
        chipColor = Colors.blue;
        break;
      case CultureStatus.completed:
        chipColor = Colors.green.shade700;
        break;
      case CultureStatus.failed:
        chipColor = Colors.red.shade700;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        culture.status.displayName,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build date information
  Widget _buildDateInfo() {
    final creationDate = _formatDate(culture.creationDate);

    if (culture.nextTransferDate != null) {
      final nextTransfer = _formatDate(culture.nextTransferDate!);
      final isOverdue = culture.nextTransferDate!.isBefore(DateTime.now());

      return Text(
        'Created: $creationDate • Next transfer: $nextTransfer',
        style: TextStyle(
          color: isOverdue ? Colors.red.shade600 : Colors.grey.shade600,
          fontSize: 12,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    } else {
      return Text(
        'Created: $creationDate',
        style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
      );
    }
  }

  /// Build trailing widget with additional info
  Widget _buildTrailing() {
    // Show warning icon if transfer is overdue
    if (culture.nextTransferDate != null &&
        culture.nextTransferDate!.isBefore(DateTime.now()) &&
        culture.status == CultureStatus.active) {
      return Icon(Icons.schedule, color: Colors.orange.shade600, size: 20);
    }

    // Show arrow for navigation
    return const Icon(Icons.chevron_right, color: Colors.grey);
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '$difference days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
