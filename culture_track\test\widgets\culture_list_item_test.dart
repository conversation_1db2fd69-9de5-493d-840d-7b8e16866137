import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_track/widgets/culture_list_item.dart';
import 'package:culture_track/models/culture.dart';

void main() {
  group('CultureListItem Tests', () {
    late Culture testCulture;

    setUp(() {
      testCulture = Culture(
        id: 1,
        species: 'Orchid',
        variety: 'Phalaenopsis',
        creationDate: DateTime.now().subtract(const Duration(days: 30)),
        currentStage: CultureStage.multiplication,
        status: CultureStatus.active,
        nextTransferDate: DateTime.now().add(const Duration(days: 7)),
        notes: 'Test culture notes',
      );
    });

    Widget createTestWidget(Culture culture, {VoidCallback? onTap}) {
      return MaterialApp(
        home: Scaffold(
          body: CultureListItem(
            culture: culture,
            onTap: onTap,
          ),
        ),
      );
    }

    testWidgets('displays culture species and variety', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should display species and variety
      expect(find.text('Orchid - Phalaenopsis'), findsOneWidget);
    });

    testWidgets('displays species only when variety is null', (WidgetTester tester) async {
      final cultureWithoutVariety = testCulture.copyWith(variety: null);
      
      await tester.pumpWidget(createTestWidget(cultureWithoutVariety));

      // Should display only species
      expect(find.text('Orchid'), findsOneWidget);
      expect(find.text('Orchid - Phalaenopsis'), findsNothing);
    });

    testWidgets('displays correct status icon for active culture', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should have science icon for active status
      expect(find.byIcon(Icons.science), findsOneWidget);
    });

    testWidgets('displays correct status icon for contaminated culture', (WidgetTester tester) async {
      final contaminatedCulture = testCulture.copyWith(status: CultureStatus.contaminated);
      
      await tester.pumpWidget(createTestWidget(contaminatedCulture));

      // Should have warning icon for contaminated status
      expect(find.byIcon(Icons.warning), findsOneWidget);
    });

    testWidgets('displays stage and status chips', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should display stage and status text
      expect(find.text('Multiplication'), findsOneWidget);
      expect(find.text('Active'), findsOneWidget);
    });

    testWidgets('displays creation date', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should display some form of creation date
      expect(find.textContaining('Created:'), findsOneWidget);
    });

    testWidgets('displays next transfer date when available', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should display next transfer date
      expect(find.textContaining('Next transfer:'), findsOneWidget);
    });

    testWidgets('does not display next transfer when null', (WidgetTester tester) async {
      final cultureWithoutTransfer = testCulture.copyWith(nextTransferDate: null);
      
      await tester.pumpWidget(createTestWidget(cultureWithoutTransfer));

      // Should not display next transfer date
      expect(find.textContaining('Next transfer:'), findsNothing);
      expect(find.textContaining('Created:'), findsOneWidget);
    });

    testWidgets('shows overdue indicator for past transfer date', (WidgetTester tester) async {
      final overdueCulture = testCulture.copyWith(
        nextTransferDate: DateTime.now().subtract(const Duration(days: 1)),
      );
      
      await tester.pumpWidget(createTestWidget(overdueCulture));

      // Should show schedule icon for overdue transfer
      expect(find.byIcon(Icons.schedule), findsOneWidget);
    });

    testWidgets('shows navigation arrow when not overdue', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should show chevron right for navigation
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('calls onTap when tapped', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(createTestWidget(
        testCulture,
        onTap: () => tapped = true,
      ));

      // Tap the list item
      await tester.tap(find.byType(ListTile));
      await tester.pump();

      // Should have called onTap
      expect(tapped, isTrue);
    });

    testWidgets('is wrapped in a Card', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should be wrapped in a Card
      expect(find.byType(Card), findsOneWidget);
    });

    testWidgets('uses ListTile for layout', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testCulture));

      // Should use ListTile
      expect(find.byType(ListTile), findsOneWidget);
    });
  });
}
