# Database Schema Design

## Overview
SQLite database schema for CultureTrack tissue culture management app.

## Tables

### 1. cultures
Primary table for culture batch tracking.

```sql
CREATE TABLE cultures (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  species TEXT NOT NULL,
  variety TEXT,
  source TEXT,
  creation_date INTEGER NOT NULL, -- Unix timestamp
  current_stage TEXT NOT NULL CHECK (current_stage IN ('initiation', 'multiplication', 'rooting', 'acclimatization')),
  status TEXT NOT NULL CHECK (status IN ('active', 'contaminated', 'transferred', 'completed', 'failed')),
  last_transfer_date INTEGER, -- Unix timestamp
  next_transfer_date INTEGER, -- Unix timestamp
  notes TEXT,
  photos TEXT, -- JSON array of photo file paths
  contamination_log TEXT, -- JSON array of contamination events
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);
```

### 2. recipes
Media recipe storage and management.

```sql
CREATE TABLE recipes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  description TEXT,
  media_type TEXT NOT NULL,
  ingredients_json TEXT NOT NULL, -- JSON object with ingredients and concentrations
  instructions TEXT,
  source TEXT,
  is_template INTEGER NOT NULL DEFAULT 0 CHECK (is_template IN (0, 1)),
  created_date INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);
```

### 3. transfer_logs
Historical record of culture transfers and stage changes.

```sql
CREATE TABLE transfer_logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  culture_id INTEGER NOT NULL,
  from_stage TEXT,
  to_stage TEXT NOT NULL,
  transfer_date INTEGER NOT NULL, -- Unix timestamp
  notes TEXT,
  photos TEXT, -- JSON array of photo file paths
  success_indicator INTEGER CHECK (success_indicator IN (0, 1)), -- 0=failed, 1=success, NULL=pending
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
  FOREIGN KEY (culture_id) REFERENCES cultures (id) ON DELETE CASCADE
);
```

### 4. notifications
Scheduled notifications for transfer reminders.

```sql
CREATE TABLE notifications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  culture_id INTEGER NOT NULL,
  notification_type TEXT NOT NULL CHECK (notification_type IN ('transfer_reminder', 'contamination_alert', 'stage_update')),
  scheduled_date INTEGER NOT NULL, -- Unix timestamp
  is_sent INTEGER NOT NULL DEFAULT 0 CHECK (is_sent IN (0, 1)),
  is_dismissed INTEGER NOT NULL DEFAULT 0 CHECK (is_dismissed IN (0, 1)),
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
  updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
  FOREIGN KEY (culture_id) REFERENCES cultures (id) ON DELETE CASCADE
);
```

## Indexes
Performance optimization indexes.

```sql
-- Culture queries
CREATE INDEX idx_cultures_status ON cultures (status);
CREATE INDEX idx_cultures_stage ON cultures (current_stage);
CREATE INDEX idx_cultures_next_transfer ON cultures (next_transfer_date);
CREATE INDEX idx_cultures_created ON cultures (creation_date);

-- Recipe queries
CREATE INDEX idx_recipes_media_type ON recipes (media_type);
CREATE INDEX idx_recipes_template ON recipes (is_template);

-- Transfer log queries
CREATE INDEX idx_transfer_logs_culture ON transfer_logs (culture_id);
CREATE INDEX idx_transfer_logs_date ON transfer_logs (transfer_date);

-- Notification queries
CREATE INDEX idx_notifications_culture ON notifications (culture_id);
CREATE INDEX idx_notifications_scheduled ON notifications (scheduled_date);
CREATE INDEX idx_notifications_sent ON notifications (is_sent);
```

## Data Types & Constraints

### Date Handling
- All dates stored as Unix timestamps (INTEGER)
- Allows for easy date calculations and timezone handling
- Default values use SQLite's strftime function

### JSON Fields
- `photos`: Array of file paths `["path1.jpg", "path2.jpg"]`
- `contamination_log`: Array of events `[{"date": 1234567890, "cause": "bacterial", "notes": "..."}]`
- `ingredients_json`: Recipe ingredients `{"MS_salts": {"amount": 4.4, "unit": "g/L"}, ...}`

### Enums via CHECK Constraints
- `current_stage`: Enforces valid culture stages
- `status`: Enforces valid culture statuses
- `notification_type`: Enforces valid notification types
- Boolean fields: Uses INTEGER with CHECK (0, 1)

## Relationships
- `transfer_logs.culture_id` → `cultures.id` (CASCADE DELETE)
- `notifications.culture_id` → `cultures.id` (CASCADE DELETE)

## Database Version
- Initial version: 1
- Migration strategy: Increment version number and handle upgrades in database helper
