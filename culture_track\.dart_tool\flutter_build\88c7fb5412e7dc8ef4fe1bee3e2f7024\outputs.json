["D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\GitHub\\CultureTrack-augment\\culture_track\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]