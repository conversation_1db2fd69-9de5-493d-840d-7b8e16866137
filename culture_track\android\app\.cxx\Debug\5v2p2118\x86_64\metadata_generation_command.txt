                        -HD:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\src\AndroidSDK\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\src\AndroidSDK\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\src\AndroidSDK\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\src\AndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\GitHub\CultureTrack-augment\culture_track\build\app\intermediates\cxx\Debug\5v2p2118\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\GitHub\CultureTrack-augment\culture_track\build\app\intermediates\cxx\Debug\5v2p2118\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\GitHub\CultureTrack-augment\culture_track\android\app\.cxx\Debug\5v2p2118\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2