import 'package:flutter_test/flutter_test.dart';
import 'package:culture_track/providers/recipe_provider.dart';

void main() {
  group('RecipeProvider Tests', () {
    late RecipeProvider provider;

    setUp(() {
      provider = RecipeProvider();
    });

    tearDown(() {
      provider.clear();
    });

    test('initial state should be empty', () {
      expect(provider.recipes, isEmpty);
      expect(provider.isLoading, false);
      expect(provider.error, null);
      expect(provider.recipesCount, 0);
      expect(provider.templatesCount, 0);
    });

    test('should filter recipes by media type', () {
      expect(provider.getRecipesByMediaType('initiation'), isEmpty);
      expect(provider.templateRecipes, isEmpty);
      expect(provider.userRecipes, isEmpty);
    });

    test('should search recipes', () {
      expect(provider.searchRecipes('test'), isEmpty);
      expect(provider.searchRecipes(''), isEmpty);
    });

    test('should get recipes for stage', () {
      expect(provider.getRecipesForStage('initiation'), isEmpty);
      expect(provider.getRecipesForStage('invalid'), isEmpty);
    });

    test('should get recipe by ID', () {
      expect(provider.getRecipeById(1), null);
    });

    test('should clear data', () {
      provider.clear();
      expect(provider.recipes, isEmpty);
      expect(provider.error, null);
      expect(provider.isLoading, false);
    });
  });
}
