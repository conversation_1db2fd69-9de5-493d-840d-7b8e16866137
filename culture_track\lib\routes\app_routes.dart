import 'package:flutter/material.dart';
import '../screens/add_culture_screen.dart';
import '../screens/add_recipe_screen.dart';
import '../screens/culture_detail_screen.dart';
import '../screens/recipe_detail_screen.dart';
import '../widgets/main_navigation.dart';

class AppRoutes {
  static const String home = '/';
  static const String addCulture = '/add-culture';
  static const String addRecipe = '/add-recipe';
  static const String cultureDetail = '/culture-detail';
  static const String recipeDetail = '/recipe-detail';

  static Map<String, WidgetBuilder> get routes {
    return {
      home: (context) => const MainNavigation(),
      addCulture: (context) => const AddCultureScreen(),
      addRecipe: (context) => const AddRecipeScreen(),
    };
  }

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case cultureDetail:
        final args = settings.arguments as Map<String, dynamic>?;
        final cultureId = args?['cultureId'] as String? ?? '1';
        return MaterialPageRoute(
          builder: (context) => CultureDetailScreen(cultureId: cultureId),
          settings: settings,
        );

      case recipeDetail:
        final args = settings.arguments as Map<String, dynamic>?;
        final recipeId = args?['recipeId'] as String? ?? '1';
        return MaterialPageRoute(
          builder: (context) => RecipeDetailScreen(recipeId: recipeId),
          settings: settings,
        );

      default:
        return null;
    }
  }

  static Route<dynamic> onUnknownRoute(RouteSettings settings) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(
          title: const Text('Page Not Found'),
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                'Page Not Found',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'The requested page could not be found.',
                style: TextStyle(
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
