import 'package:flutter/material.dart';

class AddCultureScreen extends StatefulWidget {
  const AddCultureScreen({super.key});

  @override
  State<AddCultureScreen> createState() => _AddCultureScreenState();
}

class _AddCultureScreenState extends State<AddCultureScreen> {
  final _formKey = GlobalKey<FormState>();
  final _speciesController = TextEditingController();
  final _varietyController = TextEditingController();
  final _sourceController = TextEditingController();

  @override
  void dispose() {
    _speciesController.dispose();
    _varietyController.dispose();
    _sourceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Culture'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          TextButton(
            onPressed: _saveCulture,
            child: const Text(
              'Save',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _speciesController,
                decoration: const InputDecoration(
                  labelText: 'Species',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a species';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _varietyController,
                decoration: const InputDecoration(
                  labelText: 'Variety (optional)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _sourceController,
                decoration: const InputDecoration(
                  labelText: 'Source',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a source';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveCulture,
                  child: const Text('Save Culture'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveCulture() {
    if (_formKey.currentState!.validate()) {
      // TODO: Implement culture saving logic
      // This will be implemented in Task 5
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Culture saved successfully!'),
        ),
      );
      Navigator.pop(context);
    }
  }
}
