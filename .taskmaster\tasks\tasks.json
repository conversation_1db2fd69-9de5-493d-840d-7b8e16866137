{"master": {"tasks": [{"id": 1, "title": "Define & Implement SQLite Schema", "description": "Define and implement the SQLite database schema for core entities: Culture, Recipe, Transfer Log, and Notification, using the sqflite package.", "details": "Use the `sqflite` package to create database helper class. Define SQL CREATE TABLE statements for `CultureEntity`, `RecipeEntity`, `TransferLogEntity`, and `NotificationEntity` based on the PRD data models. Implement database initialization and versioning.", "testStrategy": "Write unit tests for database helper methods (open, create tables). Manually verify schema structure using a SQLite browser.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Design Database Schema Structure", "description": "Design the complete database schema including tables for Culture, Recipe, Transfer Log, and Notification entities with proper relationships and constraints.", "details": "", "status": "done", "dependencies": [], "parentTaskId": 1}, {"id": 2, "title": "Create Database Helper Class", "description": "Implement a database helper class using sqflite package to manage database connections, initialization, and version management.", "details": "", "status": "done", "dependencies": ["1.1"], "parentTaskId": 1}, {"id": 3, "title": "Write SQL CREATE TABLE Statements", "description": "Write the SQL CREATE TABLE statements for all entities (Culture, Recipe, Transfer Log, Notification) with proper data types and constraints.", "details": "", "status": "done", "dependencies": ["1.1"], "parentTaskId": 1}, {"id": 4, "title": "Implement Database Initialization Logic", "description": "Implement the database initialization logic including table creation, initial data seeding, and database upgrade handling.", "details": "", "status": "done", "dependencies": ["1.2", "1.3"], "parentTaskId": 1}, {"id": 5, "title": "Test Database Operations", "description": "Create and run basic tests to verify database creation, table structure, and basic CRUD operations work correctly.", "details": "", "status": "done", "dependencies": ["1.4"], "parentTaskId": 1}]}, {"id": 2, "title": "Setup Flutter Navigation & Routing", "description": "Set up the basic navigation structure for the Flutter application, including bottom tab navigation and routing between main screens.", "details": "Implement a navigation structure using <PERSON><PERSON><PERSON>'s built-in Navigator or a package like `go_router`. Define routes for the main screens: Dashboard, Culture List, Recipe List, Add Culture, Culture Detail, Recipe Detail. Set up the bottom navigation bar.", "testStrategy": "Manually test navigation between all defined screens. Verify correct screen is displayed for each route.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 3, "title": "Implement Main Navigation Structure", "description": "Set up the core widget structure for main navigation, such as using a TabBarView or IndexedStack to manage different sections.", "dependencies": [1, 2], "details": "This structure will hold the main screens accessible via the bottom navigation.", "status": "done"}, {"id": 4, "title": "Setup Bottom Navigation Bar", "description": "Integrate and configure the BottomNavigationBar widget to allow users to switch between the main sections defined in the navigation structure.", "dependencies": [3], "details": "Ensure the BottomNavigationBar correctly controls the display of screens within the main navigation structure.", "status": "done"}]}, {"id": 3, "title": "Integrate Provider State Management", "description": "Successfully integrated the Provider state management pattern to manage application data flow and reactive UI updates. The integration is now fully functional and ready for use by UI components.", "status": "done", "dependencies": [], "priority": "high", "details": "The Provider pattern has been fully integrated, including adding the necessary package, creating comprehensive ChangeNotifier models for Cultures and Recipes with full CRUD, filtering, search, and advanced features (templates, contamination logging, photo management, transfers). The main app is wrapped with MultiProvider, making the state accessible throughout the widget tree. The implementation includes error handling, loading states, type-safe models, and integration with the existing database layer.", "testStrategy": "Provider unit tests for basic functionality and integration tests to verify provider accessibility in the widget tree have been successfully completed. Manual verification of data changes triggering UI updates was also performed.", "subtasks": [{"id": "3-1", "description": "Added provider package dependency to pubspec.yaml", "status": "completed"}, {"id": "3-2", "description": "Created comprehensive Culture data model with proper serialization, enums, and null safety", "status": "completed"}, {"id": "3-3", "description": "Created comprehensive Recipe data model with ingredient management", "status": "completed"}, {"id": "3-4", "description": "Implemented CultureProvider with full CRUD operations, filtering, and state management", "status": "completed"}, {"id": "3-5", "description": "Implemented RecipeProvider with template management, search, and ingredient operations", "status": "completed"}, {"id": "3-6", "description": "Wrapped main app with MultiProvider to make providers accessible throughout widget tree", "status": "completed"}, {"id": "3-7", "description": "Created provider unit tests for basic functionality", "status": "completed"}, {"id": "3-8", "description": "Created integration tests to verify providers are accessible in widget tree", "status": "completed"}, {"id": "3-9", "description": "Added ProviderDemoWidget to dashboard to demonstrate working state management", "status": "completed"}, {"id": "3-10", "description": "Fixed all code analysis issues and ensured proper null safety", "status": "completed"}]}, {"id": 4, "title": "Implement Culture List Screen", "description": "Implement the screen displaying a list of active culture batches, fetching data from the local SQLite database.", "details": "Create a Flutter screen widget. Use a Provider to fetch culture data from the SQLite database (Task 1). Display cultures in a scrollable list (e.g., ListView.builder). Include basic information like species, stage, and status. Implement navigation to the Culture Detail screen on tap.", "testStrategy": "Write widget tests to verify the list renders correctly with mock data. Manually test fetching and displaying data from the database.", "priority": "high", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Implement Culture Creation Form", "description": "Create the form and logic for adding new culture batch entries into the SQLite database.", "details": "Create a Flutter screen widget with input fields for species, variety, source, creation date, current stage, media type, and initial transfer schedule. Implement form validation. Use a Provider to handle inserting the new culture data into the SQLite database (Task 1). Navigate back to the Culture List screen upon successful creation.", "testStrategy": "Write widget tests for form validation. Manually test creating cultures with various inputs and verify data persistence in the database.", "priority": "high", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 6, "title": "Add Photo Capture & Storage", "description": "Integrate camera functionality to allow users to capture and attach photos to culture entries, storing them locally.", "details": "Use a package like `image_picker` to access the device camera. Implement functionality to capture photos and save them to local device storage. Store the file path or identifier in the Culture entity in the database (Task 1). Display captured photos on the Culture Detail screen.", "testStrategy": "Manually test photo capture and attachment on different devices. Verify photos are saved correctly and displayed on the detail screen. Test storage permissions.", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": [{"id": 1, "title": "Add Camera/Image Picker Package", "description": "Add the image_picker package to pubspec.yaml and configure platform-specific settings for camera access.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 6}, {"id": 2, "title": "Handle Camera Permissions", "description": "Implement permission handling for camera and storage access on both iOS and Android platforms.", "details": "", "status": "pending", "dependencies": ["6.1"], "parentTaskId": 6}, {"id": 3, "title": "Implement Photo Capture Functionality", "description": "Create the UI and logic for capturing photos from camera or selecting from gallery, with proper error handling.", "details": "", "status": "pending", "dependencies": ["6.2"], "parentTaskId": 6}, {"id": 4, "title": "Implement Local Photo Storage", "description": "Create functionality to save captured photos to local device storage with proper file naming and organization.", "details": "", "status": "pending", "dependencies": ["6.3"], "parentTaskId": 6}, {"id": 5, "title": "Update Database Schema for Photo Paths", "description": "Modify the Culture entity database schema to include fields for storing photo file paths and metadata.", "details": "", "status": "pending", "dependencies": ["6.4"], "parentTaskId": 6}, {"id": 6, "title": "Display Photos in Culture Detail Screen", "description": "Implement UI components to display captured photos in the culture detail screen with proper image loading and error handling.", "details": "", "status": "pending", "dependencies": ["6.5"], "parentTaskId": 6}]}, {"id": 7, "title": "Implement Culture Detail Screen (CRUD)", "description": "Implement the screen displaying detailed information for a single culture batch, including basic info, status, dates, and enabling updates and deletion.", "details": "Create a Flutter screen widget that fetches a single culture's data from the database (Task 1) based on its ID passed via navigation. Display all relevant fields (species, dates, stage, status, photos). Implement buttons/forms to update culture details and delete the culture entry. Use a Provider to handle database operations.", "testStrategy": "Manually test viewing, updating, and deleting culture entries. Verify data changes are persisted and deletion removes the entry from the list.", "priority": "high", "dependencies": [1, 4, 5, 6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Implement Culture Status & Stage Updates", "description": "Develop the system for updating a culture's stage and status, including logging these changes with timestamps.", "details": "Add UI elements on the Culture Detail screen (Task 7) to change the culture's `current_stage` and `status`. When a stage change occurs (e.g., after a transfer), create a new entry in the `TransferLogEntity` (Task 1) with timestamps and relevant details. Update the `last_transfer_date` in the Culture entity.", "testStrategy": "Manually test updating stage and status. Verify timestamps and log entries are correctly recorded in the database.", "priority": "high", "dependencies": [1, 7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Implement Transfer Date Calculation & Storage", "description": "Implement the logic for calculating and storing the next transfer date based on the current stage and customizable intervals.", "details": "Modify the Culture entity (Task 1) to store `next_transfer_date`. When a culture's stage is updated (Task 8) or a new culture is created (Task 5), calculate the `next_transfer_date` based on the `last_transfer_date` (or creation date) and a default/customizable interval for the new stage. Store this date in the database.", "testStrategy": "Write unit tests for date calculation logic with different intervals. Manually test setting/updating transfer dates and verify correct calculation and storage.", "priority": "high", "dependencies": [1, 8], "status": "pending", "subtasks": [{"id": 1, "title": "Define/Manage Stage Transfer Intervals", "description": "Determine how the transfer intervals for each stage are defined and managed (e.g., configuration file, database table). Implement the necessary data structure or configuration.", "dependencies": [], "details": "Specify the source and format of stage transfer interval data.", "status": "pending"}, {"id": 2, "title": "Implement Date Calculation Logic Function", "description": "Write a reusable function that takes the current date, current stage, and stage intervals as input and calculates the next transfer date.", "dependencies": [1], "details": "The function should handle different stages and their corresponding intervals.", "status": "pending"}, {"id": 3, "title": "Integrate Date Calculation into Culture Creation", "description": "Modify the culture creation process to call the date calculation function upon initial creation and set the first next transfer date.", "dependencies": [2], "details": "Ensure the calculation is performed when a new culture record is saved.", "status": "pending"}, {"id": 4, "title": "Integrate Date Calculation into Stage Update", "description": "Modify the culture stage update process to recalculate and update the next transfer date whenever a culture's stage is changed.", "dependencies": [2], "details": "Ensure the calculation is triggered and the date is updated when the stage field is modified.", "status": "pending"}, {"id": 5, "title": "Store Calculated Next Transfer Date", "description": "Add a dedicated field in the database schema for the culture object to store the calculated next transfer date and ensure it is persisted during creation and updates.", "dependencies": [3, 4], "details": "Update database migration/schema and ensure data is saved correctly.", "status": "pending"}]}, {"id": 10, "title": "Setup Local Notifications for Transfers", "description": "Set up the platform-native local notification system to send reminders for upcoming culture transfers.", "details": "Use a package like `flutter_local_notifications`. Implement functionality to schedule a local notification 24 hours before the `next_transfer_date` stored in the database (Task 9). Handle notification permissions and background processing if necessary to ensure timely delivery.", "testStrategy": "Manually test scheduling notifications for upcoming dates. Verify notifications are received on the device at the correct time. Test notification persistence across app restarts.", "priority": "high", "dependencies": [1, 9], "status": "pending", "subtasks": [{"id": 1, "title": "Add Local Notifications Package", "description": "Add the flutter_local_notifications package to pubspec.yaml and configure basic setup for both iOS and Android platforms.", "details": "", "status": "pending", "dependencies": [], "parentTaskId": 10}, {"id": 2, "title": "Request & Handle Notification Permissions", "description": "Implement logic to request notification permissions from the user and handle permission states for both iOS and Android.", "details": "", "status": "pending", "dependencies": ["10.1"], "parentTaskId": 10}, {"id": 3, "title": "Implement Notification Scheduling Logic", "description": "Create the core logic to schedule notifications based on culture transfer dates, including calculating notification timing (24 hours before transfer).", "details": "", "status": "pending", "dependencies": ["10.2"], "parentTaskId": 10}, {"id": 4, "title": "Handle Notification Tap Actions", "description": "Implement functionality to handle when users tap on notifications, including opening the app and navigating to the relevant culture detail screen.", "details": "", "status": "pending", "dependencies": ["10.3"], "parentTaskId": 10}, {"id": 5, "title": "Test Notification Delivery Cross-Platform", "description": "Test notification scheduling and delivery on both iOS and Android devices, ensuring proper timing and functionality across platforms.", "details": "", "status": "pending", "dependencies": ["10.4"], "parentTaskId": 10}]}, {"id": 11, "title": "Implement Upcoming Transfers View (Calendar/List)", "description": "Implement a view (list or calendar) displaying upcoming culture transfers based on the stored next transfer dates.", "details": "Create a Flutter screen or a section on the Dashboard (future task) that queries the database (Task 1) for cultures where `next_transfer_date` is in the near future. Display these cultures and their due dates in a clear format (e.g., a simple list sorted by date, or integrate a calendar widget).", "testStrategy": "Manually test the view with cultures having different upcoming transfer dates. Verify correct sorting and display of due dates.", "priority": "high", "dependencies": [1, 9], "status": "pending", "subtasks": []}, {"id": 12, "title": "Implement Media Recipe Storage (CRUD)", "description": "Implement the system for storing, retrieving, updating, and deleting media recipes in the SQLite database.", "details": "Create Flutter screens for listing, viewing details, creating, and editing `RecipeEntity` entries (Task 1). Implement forms for recipe details (name, description, ingredients_json, instructions). Use a Provider to handle database operations for recipes.", "testStrategy": "Manually test creating, viewing, updating, and deleting recipes. Verify data persistence and correct display of recipe details.", "priority": "medium", "dependencies": [1, 2, 3], "status": "pending", "subtasks": []}, {"id": 13, "title": "Implement Recipe Quantity Calculator", "description": "Develop the calculator interface and logic to scale ingredient quantities based on a selected recipe and desired preparation volume.", "details": "Create a Flutter screen or section linked from the Recipe Detail screen (Task 12). Allow the user to select a recipe and input a desired final volume. Implement logic to calculate the required quantity for each ingredient based on its concentration/ratio in the stored recipe (Task 1) and the target volume. Display the calculated quantities.", "testStrategy": "Write unit tests for the quantity calculation logic with various recipes and volumes. Manually test the calculator interface with different inputs and verify output accuracy.", "priority": "medium", "dependencies": [1, 12], "status": "pending", "subtasks": []}, {"id": 14, "title": "Add Pre-loaded Media Recipe Templates", "description": "Populate the database with pre-loaded template recipes for common media types (MS, WPM, rooting media).", "details": "Prepare JSON or structured data for common media recipes (MS, WPM, etc.). During database initialization or a specific setup step, insert these template recipes into the `RecipeEntity` table (Task 1). Mark them as templates (`is_template` flag). Ensure they are accessible via the Recipe List/Search (Task 12).", "testStrategy": "Manually verify that template recipes appear in the recipe list. Check that their details are correct and that they can be used in the calculator (Task 13).", "priority": "low", "dependencies": [1, 12], "status": "pending", "subtasks": []}, {"id": 15, "title": "Implement Notes & Contamination Logging", "description": "Implement the functionality for users to add timestamped notes and log contamination events for each culture batch.", "details": "Add UI elements on the Culture Detail screen (Task 7) to add new notes and log contamination events. Store notes and contamination logs (including suspected cause) associated with the culture ID in the database (Task 1), potentially within the Culture entity's JSON fields or a separate log table. Display a timeline or list of notes and logs on the Culture Detail screen.", "testStrategy": "Manually test adding notes and logging contamination events. Verify timestamps are correct and entries are saved and displayed correctly for the specific culture.", "priority": "medium", "dependencies": [1, 7], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-16T07:01:26.744Z", "updated": "2025-06-16T11:29:27.166Z", "description": "Tasks for master context"}}}