import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

// Table names
const String tableCultures = 'cultures';
const String tableRecipes = 'recipes';
const String tableTransferLogs = 'transfer_logs';
const String tableNotifications = 'notifications';

/// Create test database with all tables and indexes
Future<void> _createTestDatabase(Database db, int version) async {
  await _createTables(db);
  await _createIndexes(db);
  await _seedInitialData(db);
}

/// Create all database tables
Future<void> _createTables(Database db) async {
  // Create cultures table
  await db.execute('''
    CREATE TABLE $tableCultures (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      species TEXT NOT NULL,
      variety TEXT,
      source TEXT,
      creation_date INTEGER NOT NULL,
      current_stage TEXT NOT NULL CHECK (current_stage IN ('initiation', 'multiplication', 'rooting', 'acclimatization')),
      status TEXT NOT NULL CHECK (status IN ('active', 'contaminated', 'transferred', 'completed', 'failed')),
      last_transfer_date INTEGER,
      next_transfer_date INTEGER,
      notes TEXT,
      photos TEXT,
      contamination_log TEXT,
      created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
      updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
    )
  ''');

  // Create recipes table
  await db.execute('''
    CREATE TABLE $tableRecipes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      media_type TEXT NOT NULL,
      ingredients_json TEXT NOT NULL,
      instructions TEXT,
      source TEXT,
      is_template INTEGER NOT NULL DEFAULT 0 CHECK (is_template IN (0, 1)),
      created_date INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
      created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
      updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
    )
  ''');

  // Create transfer_logs table
  await db.execute('''
    CREATE TABLE $tableTransferLogs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      culture_id INTEGER NOT NULL,
      from_stage TEXT,
      to_stage TEXT NOT NULL,
      transfer_date INTEGER NOT NULL,
      notes TEXT,
      photos TEXT,
      success_indicator INTEGER CHECK (success_indicator IN (0, 1)),
      created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
      FOREIGN KEY (culture_id) REFERENCES $tableCultures (id) ON DELETE CASCADE
    )
  ''');

  // Create notifications table
  await db.execute('''
    CREATE TABLE $tableNotifications (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      culture_id INTEGER NOT NULL,
      notification_type TEXT NOT NULL CHECK (notification_type IN ('transfer_reminder', 'contamination_alert', 'stage_update')),
      scheduled_date INTEGER NOT NULL,
      is_sent INTEGER NOT NULL DEFAULT 0 CHECK (is_sent IN (0, 1)),
      is_dismissed INTEGER NOT NULL DEFAULT 0 CHECK (is_dismissed IN (0, 1)),
      title TEXT NOT NULL,
      body TEXT NOT NULL,
      created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
      updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
      FOREIGN KEY (culture_id) REFERENCES $tableCultures (id) ON DELETE CASCADE
    )
  ''');
}

/// Create database indexes for performance
Future<void> _createIndexes(Database db) async {
  // Culture indexes
  await db.execute(
    'CREATE INDEX idx_cultures_status ON $tableCultures (status)',
  );
  await db.execute(
    'CREATE INDEX idx_cultures_stage ON $tableCultures (current_stage)',
  );
  await db.execute(
    'CREATE INDEX idx_cultures_next_transfer ON $tableCultures (next_transfer_date)',
  );
  await db.execute(
    'CREATE INDEX idx_cultures_created ON $tableCultures (creation_date)',
  );

  // Recipe indexes
  await db.execute(
    'CREATE INDEX idx_recipes_media_type ON $tableRecipes (media_type)',
  );
  await db.execute(
    'CREATE INDEX idx_recipes_template ON $tableRecipes (is_template)',
  );

  // Transfer log indexes
  await db.execute(
    'CREATE INDEX idx_transfer_logs_culture ON $tableTransferLogs (culture_id)',
  );
  await db.execute(
    'CREATE INDEX idx_transfer_logs_date ON $tableTransferLogs (transfer_date)',
  );

  // Notification indexes
  await db.execute(
    'CREATE INDEX idx_notifications_culture ON $tableNotifications (culture_id)',
  );
  await db.execute(
    'CREATE INDEX idx_notifications_scheduled ON $tableNotifications (scheduled_date)',
  );
  await db.execute(
    'CREATE INDEX idx_notifications_sent ON $tableNotifications (is_sent)',
  );
}

/// Seed initial data (templates, etc.)
Future<void> _seedInitialData(Database db) async {
  final templates = [
    {
      'name': 'MS Medium (Murashige & Skoog)',
      'description': 'Standard tissue culture medium for most plant species',
      'media_type': 'initiation',
      'ingredients_json':
          '{"MS_salts": {"amount": 4.4, "unit": "g/L"}, "sucrose": {"amount": 30, "unit": "g/L"}}',
      'instructions':
          'Dissolve MS salts in distilled water, add sucrose, adjust pH to 5.8',
      'source': 'Murashige & Skoog (1962)',
      'is_template': 1,
    },
    {
      'name': 'WPM Medium (Woody Plant Medium)',
      'description': 'Specialized medium for woody plant tissue culture',
      'media_type': 'multiplication',
      'ingredients_json':
          '{"WPM_salts": {"amount": 4.0, "unit": "g/L"}, "sucrose": {"amount": 20, "unit": "g/L"}}',
      'instructions':
          'Dissolve WPM salts in distilled water, add sucrose, adjust pH to 5.2',
      'source': 'Lloyd & McCown (1980)',
      'is_template': 1,
    },
    {
      'name': 'Rooting Medium',
      'description': 'Low-salt medium for root development',
      'media_type': 'rooting',
      'ingredients_json':
          '{"MS_salts_half": {"amount": 2.2, "unit": "g/L"}, "IBA": {"amount": 0.5, "unit": "mg/L"}}',
      'instructions': 'Use half-strength MS salts, add IBA for root induction',
      'source': 'Standard rooting protocol',
      'is_template': 1,
    },
  ];

  for (final template in templates) {
    await db.insert(tableRecipes, template);
  }
}

void main() {
  group('Database Tests', () {
    late Database testDb;

    setUpAll(() {
      // Initialize FFI for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      // Create in-memory database for testing
      testDb = await databaseFactoryFfi.openDatabase(
        inMemoryDatabasePath,
        options: OpenDatabaseOptions(
          version: 1,
          onCreate: _createTestDatabase,
          onConfigure: (db) async {
            await db.execute('PRAGMA foreign_keys = ON');
          },
        ),
      );
    });

    tearDown(() async {
      await testDb.close();
    });

    test('Database initialization creates all tables', () async {
      // Check if all tables exist
      final tables = await testDb.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
      );

      final tableNames =
          tables.map((table) => table['name'] as String).toList();

      expect(tableNames, contains('cultures'));
      expect(tableNames, contains('recipes'));
      expect(tableNames, contains('transfer_logs'));
      expect(tableNames, contains('notifications'));
    });

    test('Database creates indexes correctly', () async {
      // Check if indexes exist
      final indexes = await testDb.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'",
      );

      final indexNames =
          indexes.map((index) => index['name'] as String).toList();

      expect(indexNames, contains('idx_cultures_status'));
      expect(indexNames, contains('idx_cultures_stage'));
      expect(indexNames, contains('idx_recipes_media_type'));
      expect(indexNames, contains('idx_transfer_logs_culture'));
      expect(indexNames, contains('idx_notifications_scheduled'));
    });

    test('Database seeds initial recipe templates', () async {
      // Check if recipe templates were inserted
      final templates = await testDb.query(
        tableRecipes,
        where: 'is_template = ?',
        whereArgs: [1],
      );

      expect(templates.length, greaterThan(0));

      // Check for specific templates
      final templateNames = templates.map((t) => t['name'] as String).toList();
      expect(templateNames, contains('MS Medium (Murashige & Skoog)'));
      expect(templateNames, contains('WPM Medium (Woody Plant Medium)'));
      expect(templateNames, contains('Rooting Medium'));
    });

    test('Can insert and retrieve culture data', () async {
      // Insert test culture
      final cultureData = {
        'species': 'Arabidopsis thaliana',
        'variety': 'Columbia',
        'source': 'Seed',
        'creation_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'current_stage': 'initiation',
        'status': 'active',
        'notes': 'Test culture for database validation',
      };

      final cultureId = await testDb.insert(tableCultures, cultureData);
      expect(cultureId, greaterThan(0));

      // Retrieve the culture
      final cultures = await testDb.query(
        tableCultures,
        where: 'id = ?',
        whereArgs: [cultureId],
      );

      expect(cultures.length, equals(1));
      expect(cultures.first['species'], equals('Arabidopsis thaliana'));
      expect(cultures.first['current_stage'], equals('initiation'));
      expect(cultures.first['status'], equals('active'));
    });

    test('Foreign key constraints work correctly', () async {
      final db = await dbHelper.database;

      // Insert a culture first
      final cultureId = await db.insert(DatabaseHelper.tableCultures, {
        'species': 'Test Species',
        'creation_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'current_stage': 'initiation',
        'status': 'active',
      });

      // Insert transfer log with valid culture_id
      final transferLogId = await db.insert(DatabaseHelper.tableTransferLogs, {
        'culture_id': cultureId,
        'to_stage': 'multiplication',
        'transfer_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'notes': 'Test transfer',
      });

      expect(transferLogId, greaterThan(0));

      // Try to insert transfer log with invalid culture_id (should fail)
      try {
        await db.insert(DatabaseHelper.tableTransferLogs, {
          'culture_id': 99999, // Non-existent culture
          'to_stage': 'multiplication',
          'transfer_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        });
        fail('Should have thrown foreign key constraint error');
      } catch (e) {
        expect(e.toString(), contains('FOREIGN KEY constraint failed'));
      }
    });

    test('Check constraints work correctly', () async {
      final db = await dbHelper.database;

      // Try to insert culture with invalid stage (should fail)
      try {
        await db.insert(DatabaseHelper.tableCultures, {
          'species': 'Test Species',
          'creation_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          'current_stage': 'invalid_stage', // Invalid stage
          'status': 'active',
        });
        fail('Should have thrown check constraint error');
      } catch (e) {
        expect(e.toString(), contains('CHECK constraint failed'));
      }

      // Try to insert culture with invalid status (should fail)
      try {
        await db.insert(DatabaseHelper.tableCultures, {
          'species': 'Test Species',
          'creation_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          'current_stage': 'initiation',
          'status': 'invalid_status', // Invalid status
        });
        fail('Should have thrown check constraint error');
      } catch (e) {
        expect(e.toString(), contains('CHECK constraint failed'));
      }
    });

    test('Database path is accessible', () async {
      final path = await dbHelper.getDatabasePath();
      expect(path, isNotEmpty);
      expect(path, contains('culture_track.db'));
    });

    test('Database can be closed and reopened', () async {
      // Open database
      final db1 = await dbHelper.database;
      expect(db1.isOpen, isTrue);

      // Close database
      await dbHelper.close();

      // Reopen database
      final db2 = await dbHelper.database;
      expect(db2.isOpen, isTrue);

      // Should be able to query
      final result = await db2.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseHelper.tableRecipes}',
      );
      expect(result.first['count'], greaterThan(0));
    });
  });
}
