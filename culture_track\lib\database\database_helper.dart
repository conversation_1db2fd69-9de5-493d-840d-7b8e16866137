import 'dart:async';
import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';

/// Database helper class for managing SQLite database operations
/// Implements singleton pattern for single database instance
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  // Database configuration
  static const String _databaseName = 'culture_track.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String tableCultures = 'cultures';
  static const String tableRecipes = 'recipes';
  static const String tableTransferLogs = 'transfer_logs';
  static const String tableNotifications = 'notifications';

  DatabaseHelper._internal();

  factory DatabaseHelper() {
    return _instance;
  }

  /// Get database instance, create if doesn't exist
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database with proper configuration
  Future<Database> _initDatabase() async {
    // Get the documents directory path
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);

    // Open/create the database
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onConfigure: _onConfigure,
    );
  }

  /// Configure database settings (foreign keys, etc.)
  Future<void> _onConfigure(Database db) async {
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
  }

  /// Create database tables on first run
  Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
    await _createIndexes(db);
    await _seedInitialData(db);
  }

  /// Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here
    // For now, we'll just recreate tables (data loss)
    // In production, implement proper migration logic
    if (oldVersion < newVersion) {
      await _dropTables(db);
      await _createTables(db);
      await _createIndexes(db);
      await _seedInitialData(db);
    }
  }

  /// Create all database tables
  Future<void> _createTables(Database db) async {
    // Create cultures table
    await db.execute('''
      CREATE TABLE $tableCultures (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        species TEXT NOT NULL,
        variety TEXT,
        source TEXT,
        creation_date INTEGER NOT NULL,
        current_stage TEXT NOT NULL CHECK (current_stage IN ('initiation', 'multiplication', 'rooting', 'acclimatization')),
        status TEXT NOT NULL CHECK (status IN ('active', 'contaminated', 'transferred', 'completed', 'failed')),
        last_transfer_date INTEGER,
        next_transfer_date INTEGER,
        notes TEXT,
        photos TEXT,
        contamination_log TEXT,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      )
    ''');

    // Create recipes table
    await db.execute('''
      CREATE TABLE $tableRecipes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        media_type TEXT NOT NULL,
        ingredients_json TEXT NOT NULL,
        instructions TEXT,
        source TEXT,
        is_template INTEGER NOT NULL DEFAULT 0 CHECK (is_template IN (0, 1)),
        created_date INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
      )
    ''');

    // Create transfer_logs table
    await db.execute('''
      CREATE TABLE $tableTransferLogs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        culture_id INTEGER NOT NULL,
        from_stage TEXT,
        to_stage TEXT NOT NULL,
        transfer_date INTEGER NOT NULL,
        notes TEXT,
        photos TEXT,
        success_indicator INTEGER CHECK (success_indicator IN (0, 1)),
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (culture_id) REFERENCES $tableCultures (id) ON DELETE CASCADE
      )
    ''');

    // Create notifications table
    await db.execute('''
      CREATE TABLE $tableNotifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        culture_id INTEGER NOT NULL,
        notification_type TEXT NOT NULL CHECK (notification_type IN ('transfer_reminder', 'contamination_alert', 'stage_update')),
        scheduled_date INTEGER NOT NULL,
        is_sent INTEGER NOT NULL DEFAULT 0 CHECK (is_sent IN (0, 1)),
        is_dismissed INTEGER NOT NULL DEFAULT 0 CHECK (is_dismissed IN (0, 1)),
        title TEXT NOT NULL,
        body TEXT NOT NULL,
        created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
        FOREIGN KEY (culture_id) REFERENCES $tableCultures (id) ON DELETE CASCADE
      )
    ''');
  }

  /// Create database indexes for performance
  Future<void> _createIndexes(Database db) async {
    // Culture indexes
    await db.execute(
      'CREATE INDEX idx_cultures_status ON $tableCultures (status)',
    );
    await db.execute(
      'CREATE INDEX idx_cultures_stage ON $tableCultures (current_stage)',
    );
    await db.execute(
      'CREATE INDEX idx_cultures_next_transfer ON $tableCultures (next_transfer_date)',
    );
    await db.execute(
      'CREATE INDEX idx_cultures_created ON $tableCultures (creation_date)',
    );

    // Recipe indexes
    await db.execute(
      'CREATE INDEX idx_recipes_media_type ON $tableRecipes (media_type)',
    );
    await db.execute(
      'CREATE INDEX idx_recipes_template ON $tableRecipes (is_template)',
    );

    // Transfer log indexes
    await db.execute(
      'CREATE INDEX idx_transfer_logs_culture ON $tableTransferLogs (culture_id)',
    );
    await db.execute(
      'CREATE INDEX idx_transfer_logs_date ON $tableTransferLogs (transfer_date)',
    );

    // Notification indexes
    await db.execute(
      'CREATE INDEX idx_notifications_culture ON $tableNotifications (culture_id)',
    );
    await db.execute(
      'CREATE INDEX idx_notifications_scheduled ON $tableNotifications (scheduled_date)',
    );
    await db.execute(
      'CREATE INDEX idx_notifications_sent ON $tableNotifications (is_sent)',
    );
  }

  /// Seed initial data (templates, etc.)
  Future<void> _seedInitialData(Database db) async {
    // Insert common media recipe templates
    await _insertRecipeTemplates(db);
  }

  /// Insert common media recipe templates
  Future<void> _insertRecipeTemplates(Database db) async {
    final templates = [
      {
        'name': 'MS Medium (Murashige & Skoog)',
        'description': 'Standard tissue culture medium for most plant species',
        'media_type': 'initiation',
        'ingredients_json': '''
{
  "MS_salts": {"amount": 4.4, "unit": "g/L"},
  "sucrose": {"amount": 30, "unit": "g/L"},
  "agar": {"amount": 8, "unit": "g/L"},
  "pH": {"amount": 5.8, "unit": ""}
}''',
        'instructions':
            '1. Dissolve MS salts in distilled water\n2. Add sucrose and mix\n3. Adjust pH to 5.8\n4. Add agar and autoclave at 121°C for 15 minutes',
        'source': 'Murashige & Skoog (1962)',
        'is_template': 1,
      },
      {
        'name': 'WPM Medium (Woody Plant Medium)',
        'description': 'Specialized medium for woody plant tissue culture',
        'media_type': 'multiplication',
        'ingredients_json': '''
{
  "WPM_salts": {"amount": 4.0, "unit": "g/L"},
  "sucrose": {"amount": 20, "unit": "g/L"},
  "agar": {"amount": 6, "unit": "g/L"},
  "pH": {"amount": 5.2, "unit": ""}
}''',
        'instructions':
            '1. Dissolve WPM salts in distilled water\n2. Add sucrose\n3. Adjust pH to 5.2\n4. Add agar and autoclave',
        'source': 'Lloyd & McCown (1980)',
        'is_template': 1,
      },
      {
        'name': 'Rooting Medium',
        'description': 'Low-salt medium for root development',
        'media_type': 'rooting',
        'ingredients_json': '''
{
  "MS_salts_half": {"amount": 2.2, "unit": "g/L"},
  "sucrose": {"amount": 15, "unit": "g/L"},
  "agar": {"amount": 6, "unit": "g/L"},
  "IBA": {"amount": 0.5, "unit": "mg/L"},
  "pH": {"amount": 5.8, "unit": ""}
}''',
        'instructions':
            '1. Use half-strength MS salts\n2. Add reduced sucrose\n3. Add IBA for root induction\n4. Adjust pH and autoclave',
        'source': 'Standard rooting protocol',
        'is_template': 1,
      },
    ];

    for (final template in templates) {
      await db.insert(tableRecipes, template);
    }
  }

  /// Drop all tables (for migrations)
  Future<void> _dropTables(Database db) async {
    await db.execute('DROP TABLE IF EXISTS $tableNotifications');
    await db.execute('DROP TABLE IF EXISTS $tableTransferLogs');
    await db.execute('DROP TABLE IF EXISTS $tableRecipes');
    await db.execute('DROP TABLE IF EXISTS $tableCultures');
  }

  /// Close database connection
  Future<void> close() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  /// Get database path for debugging
  Future<String> getDatabasePath() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    return join(documentsDirectory.path, _databaseName);
  }

  /// Delete database file (for testing/reset)
  Future<void> deleteDatabase() async {
    String path = await getDatabasePath();
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
