# Overview

Plant tissue culture practitioners, from hobbyists to small-scale commercial growers, struggle with managing multiple culture batches across different growth stages. The primary pain points include missed transfer schedules (leading to culture loss), difficulty tracking culture progress, and scattered media recipe information. This mobile application solves these critical problems by providing a centralized, offline-capable system for culture tracking, transfer scheduling, and media recipe management.

The app targets hobbyists and small-scale businesses managing 10-100+ culture batches simultaneously, who need reliable tools that work in lab environments with limited internet connectivity. Success will be measured by user retention (>60% after 30 days), active culture tracking (>15 cultures per user), and positive user feedback on preventing culture loss.

# Core Features

## Culture Tracking & Management
**What it does**: Creates and maintains digital records for individual culture batches with unique IDs, species information, current growth stage, and status updates.

**Why it's important**: Prevents culture loss through better organization and provides historical data for improving success rates. Many practitioners lose track of cultures or forget critical details about their progress.

**How it works**: Users create culture entries with basic information (species, source, date), update stages (initiation → multiplication → rooting → acclimatization), and record status changes with timestamps and photos.

## Transfer Scheduling & Notifications
**What it does**: Manages transfer schedules with customizable intervals and sends push notifications when transfers are due.

**Why it's important**: Missed transfers are the #1 cause of culture failure. Regular transfers prevent contamination and maintain culture viability.

**How it works**: Users set transfer dates based on culture stage, receive notifications 24 hours before due dates, and can view upcoming transfers in calendar format. System suggests typical intervals but allows customization.

## Media Recipe Management
**What it does**: Stores media recipes with ingredient lists and provides quantity calculations for different volumes.

**Why it's important**: Recipe accuracy is critical for culture success, and practitioners often work with multiple media types that require precise measurements.

**How it works**: Users create/store recipes with ingredients and concentrations, calculate scaled quantities for desired volumes, and access pre-loaded templates for common media types (MS, WPM, rooting media).

## Record Keeping & Documentation
**What it does**: Maintains timestamped notes, observations, and contamination logs for each culture batch.

**Why it's important**: Historical data helps identify successful protocols and contamination patterns, enabling continuous improvement.

**How it works**: Users add notes and photos to cultures, log contamination events with suspected causes, and view culture timelines showing all recorded changes.

## Dashboard & Analytics
**What it does**: Provides overview of active cultures, urgent tasks, and basic performance statistics.

**Why it's important**: Quick visual assessment helps prioritize lab work and identifies trends in culture performance.

**How it works**: Dashboard displays summary cards (active cultures, due transfers, contamination rates), recent activity feed, and basic analytics showing success rates by species and media type.

# User Experience

## User Personas

**Primary Persona - Hobbyist Enthusiast**
- Age: 25-55, plant enthusiast with basic lab setup
- Manages 15-50 cultures simultaneously
- Works evenings/weekends, needs reminders and simple tracking
- Values ease of use over advanced features

**Secondary Persona - Small Commercial Grower**
- Age: 30-60, running small plant propagation business
- Manages 50-200+ cultures across multiple species
- Needs reliable data for business operations
- Values efficiency and basic reporting capabilities

## Key User Flows

**New Culture Creation Flow**:
1. User taps "Add Culture" from dashboard
2. Enters species name, variety, source material
3. Selects current stage and media type
4. Optionally adds photo and notes
5. Sets initial transfer schedule
6. Culture appears in main list with status indicators

**Transfer Management Flow**:
1. User receives notification for due transfer
2. Opens app to see transfer details
3. Updates culture status and stage
4. Adds observations and photos
5. Sets next transfer date
6. System logs transfer completion

**Media Preparation Flow**:
1. User searches for recipe by name or type
2. Selects recipe and enters desired volume
3. Views calculated ingredient quantities
4. Optionally modifies recipe and saves as new version
5. Uses recipe during media preparation

## UI/UX Considerations

- **Mobile-first design** optimized for one-handed use in lab environments
- **High contrast interface** for visibility under various lighting conditions
- **Large touch targets** accommodate gloved hands
- **Offline-first functionality** works without internet connectivity
- **Minimal data entry** with smart defaults and quick-select options
- **Visual status indicators** for quick assessment of culture health
- **Swipe gestures** for common actions (mark complete, add note)

# Technical Architecture

## System Components

**Frontend**: Flutter framework providing native iOS and Android applications with shared codebase, optimized for offline-first operation.

**Local Storage**: SQLite database for culture data, media recipes, and user settings with full offline capability.

**State Management**: Provider pattern for reactive UI updates and data flow management.

**File Storage**: Local device storage for photos with automatic compression and cloud backup capability.

**Notifications**: Platform-native push notifications for transfer reminders and critical alerts.

**Sync Engine**: Background synchronization for data backup and multi-device access (future enhancement).

## Data Models

**Culture Entity**:
- ID (auto-generated), species, variety, source, creation_date
- current_stage, status, last_transfer_date, next_transfer_date
- notes, photos, contamination_log

**Recipe Entity**:
- ID, name, description, media_type, ingredients_json
- instructions, source, created_date, is_template

**Transfer Log Entity**:
- culture_id, from_stage, to_stage, transfer_date
- notes, photos, success_indicator

**Notification Entity**:
- culture_id, notification_type, scheduled_date, is_sent, is_dismissed

## APIs and Integrations

**Local APIs**: SQLite queries through Flutter sqflite package for all data operations.

**Device APIs**: Camera integration for photo capture, local notification scheduling, file system access for data export.

**Future Cloud APIs**: REST endpoints for data synchronization, user authentication, and backup services.

## Infrastructure Requirements

**Development**: Flutter SDK, Android Studio/Xcode, SQLite browser for database management.

**Deployment**: Google Play Store and Apple App Store distribution channels.

**Future Infrastructure**: Cloud database (Firebase/Supabase), image storage (AWS S3/CloudFlare), push notification services.

# Development Roadmap

## Phase 1: Core Foundation (MVP Base)
**Culture Management Core**:
- SQLite database setup with core entities
- Basic culture CRUD operations (create, read, update, delete)
- Simple list view showing active cultures
- Culture detail screen with basic information display
- Photo capture and storage functionality

**Navigation Framework**:
- Bottom tab navigation structure
- Basic routing between screens
- State management setup with Provider

**Goal**: Users can create, view, and basic modify culture records with photos.

## Phase 2: Transfer Scheduling System
**Scheduling Engine**:
- Transfer date calculation and storage
- Local notification system setup
- Calendar view implementation
- Due transfer identification and alerts

**Status Management**:
- Culture stage progression tracking
- Status update workflows
- Transfer logging with timestamps

**Goal**: Users receive reliable transfer reminders and can track culture progression.

## Phase 3: Media Recipe Management
**Recipe System**:
- Recipe storage and retrieval
- Ingredient quantity calculations
- Pre-loaded template recipes
- Recipe search and filtering

**Calculator Interface**:
- Volume-based quantity scaling
- Unit conversion support
- Export/share recipe functionality

**Goal**: Users can store, calculate, and access media recipes during preparation.

## Phase 4: Documentation & Analytics
**Record Keeping**:
- Note-taking system with timestamps
- Contamination logging and categorization
- Culture timeline/history view

**Basic Analytics**:
- Success rate calculations
- Contamination trend analysis
- Dashboard summary statistics

**Goal**: Users can maintain detailed records and gain insights into culture performance.

## Phase 5: Polish & Optimization
**User Experience Enhancements**:
- Improved UI/UX based on user feedback
- Performance optimization for large datasets
- Advanced filtering and search capabilities
- Data export functionality

**Quality Assurance**:
- Comprehensive testing across devices
- Bug fixes and stability improvements
- App store optimization and submission

**Goal**: Production-ready application with excellent user experience.

## Future Enhancements (Post-MVP)
- Cloud synchronization and backup
- Multi-user collaboration features
- Equipment maintenance tracking
- Inventory management system
- Advanced analytics and reporting
- Integration with lab equipment
- Community recipe sharing

# Logical Dependency Chain

## Foundation First (Weeks 1-2)
1. **Database Schema & Models** - Must be established before any feature development
2. **Basic Navigation Structure** - Required for all user interactions
3. **State Management Setup** - Needed for reactive UI updates

## Quick Visible Progress (Weeks 3-4)
4. **Culture List View** - First functional screen users will see
5. **Culture Creation Form** - Enables users to start adding data
6. **Photo Integration** - Adds immediate visual value

## Core Functionality (Weeks 5-8)
7. **Culture Detail Screen** - Builds upon list view, enables full CRUD
8. **Status Update System** - Essential for culture tracking
9. **Transfer Date Management** - Critical MVP feature

## Notification System (Weeks 9-10)
10. **Local Notification Setup** - Requires completed culture and transfer systems
11. **Calendar View** - Depends on transfer scheduling being functional

## Recipe Management (Weeks 11-12)
12. **Recipe Storage** - Independent system that can be built in parallel
13. **Recipe Calculator** - Builds upon recipe storage
14. **Template Integration** - Final recipe system component

## Documentation & Polish (Weeks 13-16)
15. **Notes and Logging** - Enhances existing culture system
16. **Basic Analytics** - Requires historical data from other systems
17. **Final UI Polish** - Last step before release

Each feature is designed to be atomic and functional on its own while building toward the complete system. Users should have a working app after each major phase, with increasing functionality.

# Risks and Mitigations

## Technical Challenges

**Risk**: Offline data synchronization complexity when cloud features are added.
**Mitigation**: Build with offline-first architecture from start. Use established patterns like event sourcing for data consistency. Implement robust conflict resolution strategies.

**Risk**: Performance issues with large datasets (hundreds of cultures with photos).
**Mitigation**: Implement pagination for culture lists, image compression and lazy loading, database indexing on frequently queried fields. Load testing with realistic data volumes.

**Risk**: Cross-platform consistency issues between iOS and Android.
**Mitigation**: Extensive testing on both platforms throughout development. Use Flutter's material design components for consistency. Implement platform-specific optimizations where needed.

## MVP Definition and Scope

**Risk**: Feature creep expanding beyond manageable MVP scope.
**Mitigation**: Strict adherence to defined user stories and acceptance criteria. Regular stakeholder reviews to validate feature necessity. Time-box development phases with clear deliverables.

**Risk**: MVP too limited to demonstrate value to users.
**Mitigation**: Focus on solving the core problem (missed transfers) completely rather than partially solving many problems. Validate MVP scope with potential users early. Plan clear upgrade path to additional features.

**Risk**: User adoption challenges due to data entry overhead.
**Mitigation**: Minimize required fields for culture creation. Implement smart defaults and templates. Design for quick data entry with mobile-optimized interfaces.

## Resource Constraints

**Risk**: Single developer bandwidth limiting development speed.
**Mitigation**: Prioritize features with highest impact. Use established libraries and frameworks to reduce development time. Focus on core functionality over visual polish in MVP.

**Risk**: Limited budget for third-party services and tools.
**Mitigation**: Use free tiers of cloud services initially. Implement local-first architecture to reduce dependency on paid services. Plan monetization strategy to support ongoing development.

**Risk**: Lack of tissue culture domain expertise in development team.
**Mitigation**: Engage with target users throughout development for feedback and validation. Research existing workflows and best practices. Join tissue culture communities for ongoing insight.

# Appendix

## Research Findings

**User Pain Points** (from tissue culture community forums and discussions):
- 73% of culture failures attributed to missed transfers
- Recipe management cited as major time sink in preparation
- Lack of standardized record-keeping leading to repeated mistakes
- Difficulty scaling from hobby to commercial operations

**Competitive Analysis**:
- No direct competitors in tissue culture space
- General lab management software too complex for target users
- Plant care apps lack tissue culture specific features
- Opportunity for specialized solution

## Technical Specifications

**Minimum Device Requirements**:
- iOS 12+ / Android API level 21+
- 2GB RAM for smooth performance
- 1GB available storage
- Camera access for photo documentation
- Push notification capability

**Database Schema Considerations**:
- Normalized design for data integrity
- JSON fields for flexible metadata storage
- Proper indexing for query performance
- Migration strategy for schema updates

**Performance Targets**:
- App launch time: <3 seconds cold start
- Database queries: <100ms for typical operations
- Photo upload: <30 seconds on 3G connection
- Offline functionality: 100% for core features

**Security Considerations**:
- Local data encryption for sensitive information
- Secure photo storage with appropriate permissions
- No user authentication required for MVP (local device only)
- Future cloud sync will require proper authentication and encryption
