import 'package:flutter/material.dart';

class CultureListScreen extends StatelessWidget {
  const CultureListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cultures'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.pushNamed(context, '/add-culture');
            },
            tooltip: 'Add Culture',
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.science,
              size: 64,
              color: Colors.grey,
            ),
            Sized<PERSON><PERSON>(height: 16),
            Text(
              'No cultures yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Sized<PERSON><PERSON>(height: 8),
            Text(
              'Tap the + button to add your first culture',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/add-culture');
        },
        tooltip: 'Add Culture',
        child: const Icon(Icons.add),
      ),
    );
  }
}
