import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/culture_provider.dart';
import '../widgets/culture_list_item.dart';
import '../routes/app_routes.dart';

class CultureListScreen extends StatefulWidget {
  const CultureListScreen({super.key});

  @override
  State<CultureListScreen> createState() => _CultureListScreenState();
}

class _CultureListScreenState extends State<CultureListScreen> {
  @override
  void initState() {
    super.initState();
    // Load cultures when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CultureProvider>().loadCultures();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cultures'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.addCulture);
            },
            tooltip: 'Add Culture',
          ),
        ],
      ),
      body: Consumer<CultureProvider>(
        builder: (context, cultureProvider, child) {
          if (cultureProvider.isLoading) {
            return _buildLoadingState();
          }

          if (cultureProvider.error != null) {
            return _buildErrorState(cultureProvider.error!, cultureProvider);
          }

          if (cultureProvider.cultures.isEmpty) {
            return _buildEmptyState();
          }

          return _buildCultureList(cultureProvider);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRoutes.addCulture);
        },
        tooltip: 'Add Culture',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build loading state widget
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading cultures...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// Build error state widget
  Widget _buildErrorState(String error, CultureProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          const Text(
            'Error loading cultures',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              provider.loadCultures();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build empty state widget
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.science, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No cultures yet',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Tap the + button to add your first culture',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// Build culture list widget
  Widget _buildCultureList(CultureProvider provider) {
    return RefreshIndicator(
      onRefresh: () async {
        await provider.loadCultures();
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        itemCount: provider.cultures.length,
        itemBuilder: (context, index) {
          final culture = provider.cultures[index];
          return CultureListItem(
            culture: culture,
            onTap: () {
              _navigateToCultureDetail(culture.id!);
            },
          );
        },
      ),
    );
  }

  /// Navigate to culture detail screen
  void _navigateToCultureDetail(int cultureId) {
    Navigator.pushNamed(
      context,
      AppRoutes.cultureDetail,
      arguments: {'cultureId': cultureId.toString()},
    );
  }
}
