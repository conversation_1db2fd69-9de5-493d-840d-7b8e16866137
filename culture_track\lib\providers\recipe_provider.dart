import 'package:flutter/foundation.dart';
import '../models/recipe.dart';
import '../database/database_helper.dart';

/// Provider for managing Recipe data and state
/// Implements ChangeNotifier for reactive UI updates
class RecipeProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  List<Recipe> _recipes = [];
  bool _isLoading = false;
  String? _error;

  /// Get all recipes
  List<Recipe> get recipes => List.unmodifiable(_recipes);

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Get recipes by media type
  List<Recipe> getRecipesByMediaType(String mediaType) {
    return _recipes.where((recipe) => recipe.mediaType == mediaType).toList();
  }

  /// Get template recipes only
  List<Recipe> get templateRecipes {
    return _recipes.where((recipe) => recipe.isTemplate).toList();
  }

  /// Get user-created recipes only
  List<Recipe> get userRecipes {
    return _recipes.where((recipe) => !recipe.isTemplate).toList();
  }

  /// Get recipes count
  int get recipesCount => _recipes.length;

  /// Get templates count
  int get templatesCount => templateRecipes.length;

  /// Load all recipes from database
  Future<void> loadRecipes() async {
    _setLoading(true);
    _clearError();

    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableRecipes,
        orderBy: 'is_template DESC, created_at DESC', // Templates first, then by creation date
      );

      _recipes = maps.map((map) => Recipe.fromMap(map)).toList();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load recipes: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new recipe
  Future<Recipe?> addRecipe(Recipe recipe) async {
    _clearError();

    try {
      final db = await _databaseHelper.database;
      final id = await db.insert(
        DatabaseHelper.tableRecipes,
        recipe.toMap(),
      );

      final newRecipe = recipe.copyWith(id: id);
      
      // Insert in appropriate position (templates first, then user recipes)
      if (recipe.isTemplate) {
        final templateIndex = _recipes.indexWhere((r) => !r.isTemplate);
        if (templateIndex == -1) {
          _recipes.add(newRecipe);
        } else {
          _recipes.insert(templateIndex, newRecipe);
        }
      } else {
        _recipes.insert(templateRecipes.length, newRecipe);
      }
      
      notifyListeners();
      return newRecipe;
    } catch (e) {
      _setError('Failed to add recipe: $e');
      return null;
    }
  }

  /// Update an existing recipe
  Future<bool> updateRecipe(Recipe recipe) async {
    if (recipe.id == null) return false;
    
    _clearError();

    try {
      final db = await _databaseHelper.database;
      final updatedRecipe = recipe.copyWith(updatedAt: DateTime.now());
      
      await db.update(
        DatabaseHelper.tableRecipes,
        updatedRecipe.toMap(),
        where: 'id = ?',
        whereArgs: [recipe.id],
      );

      final index = _recipes.indexWhere((r) => r.id == recipe.id);
      if (index != -1) {
        _recipes[index] = updatedRecipe;
        notifyListeners();
      }
      
      return true;
    } catch (e) {
      _setError('Failed to update recipe: $e');
      return false;
    }
  }

  /// Delete a recipe
  Future<bool> deleteRecipe(int recipeId) async {
    _clearError();

    try {
      final db = await _databaseHelper.database;
      await db.delete(
        DatabaseHelper.tableRecipes,
        where: 'id = ?',
        whereArgs: [recipeId],
      );

      _recipes.removeWhere((recipe) => recipe.id == recipeId);
      notifyListeners();
      
      return true;
    } catch (e) {
      _setError('Failed to delete recipe: $e');
      return false;
    }
  }

  /// Get recipe by ID
  Recipe? getRecipeById(int id) {
    try {
      return _recipes.firstWhere((recipe) => recipe.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Create a copy of a template recipe for customization
  Future<Recipe?> createFromTemplate(int templateId, String newName) async {
    final template = getRecipeById(templateId);
    if (template == null || !template.isTemplate) return null;

    final newRecipe = template.copyWith(
      id: null, // Remove ID to create new record
      name: newName,
      isTemplate: false,
      createdDate: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await addRecipe(newRecipe);
  }

  /// Search recipes by name or description
  List<Recipe> searchRecipes(String query) {
    if (query.isEmpty) return recipes;
    
    final lowerQuery = query.toLowerCase();
    return _recipes.where((recipe) {
      return recipe.name.toLowerCase().contains(lowerQuery) ||
             (recipe.description?.toLowerCase().contains(lowerQuery) ?? false) ||
             recipe.mediaType.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// Get recipes suitable for a specific culture stage
  List<Recipe> getRecipesForStage(String stage) {
    // Map culture stages to media types
    String mediaType;
    switch (stage.toLowerCase()) {
      case 'initiation':
        mediaType = 'initiation';
        break;
      case 'multiplication':
        mediaType = 'multiplication';
        break;
      case 'rooting':
        mediaType = 'rooting';
        break;
      case 'acclimatization':
        mediaType = 'acclimatization';
        break;
      default:
        return [];
    }
    
    return getRecipesByMediaType(mediaType);
  }

  /// Add ingredient to recipe
  Future<bool> addIngredientToRecipe(
    int recipeId, 
    String ingredientName, 
    Ingredient ingredient
  ) async {
    final recipe = getRecipeById(recipeId);
    if (recipe == null) return false;

    final updatedIngredients = Map<String, Ingredient>.from(recipe.ingredients);
    updatedIngredients[ingredientName] = ingredient;

    return await updateRecipe(recipe.copyWith(ingredients: updatedIngredients));
  }

  /// Remove ingredient from recipe
  Future<bool> removeIngredientFromRecipe(int recipeId, String ingredientName) async {
    final recipe = getRecipeById(recipeId);
    if (recipe == null) return false;

    final updatedIngredients = Map<String, Ingredient>.from(recipe.ingredients);
    updatedIngredients.remove(ingredientName);

    return await updateRecipe(recipe.copyWith(ingredients: updatedIngredients));
  }

  /// Update ingredient in recipe
  Future<bool> updateIngredientInRecipe(
    int recipeId, 
    String ingredientName, 
    Ingredient ingredient
  ) async {
    final recipe = getRecipeById(recipeId);
    if (recipe == null || !recipe.ingredients.containsKey(ingredientName)) {
      return false;
    }

    final updatedIngredients = Map<String, Ingredient>.from(recipe.ingredients);
    updatedIngredients[ingredientName] = ingredient;

    return await updateRecipe(recipe.copyWith(ingredients: updatedIngredients));
  }

  /// Clear all data (useful for testing)
  void clear() {
    _recipes.clear();
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
