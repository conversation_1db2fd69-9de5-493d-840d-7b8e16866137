import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() {
  group('Simple Database Tests', () {
    late Database testDb;

    setUpAll(() {
      // Initialize FFI for testing
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      // Create in-memory database for testing
      testDb = await databaseFactoryFfi.openDatabase(
        inMemoryDatabasePath,
        options: OpenDatabaseOptions(
          version: 1,
          onCreate: (db, version) async {
            // Create a simple cultures table for testing
            await db.execute('''
              CREATE TABLE cultures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                species TEXT NOT NULL,
                variety TEXT,
                creation_date INTEGER NOT NULL,
                current_stage TEXT NOT NULL CHECK (current_stage IN ('initiation', 'multiplication', 'rooting', 'acclimatization')),
                status TEXT NOT NULL CHECK (status IN ('active', 'contaminated', 'transferred', 'completed', 'failed'))
              )
            ''');
            
            // Create a simple recipes table for testing
            await db.execute('''
              CREATE TABLE recipes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                media_type TEXT NOT NULL,
                is_template INTEGER NOT NULL DEFAULT 0 CHECK (is_template IN (0, 1))
              )
            ''');
            
            // Insert test recipe
            await db.insert('recipes', {
              'name': 'MS Medium',
              'media_type': 'initiation',
              'is_template': 1,
            });
          },
          onConfigure: (db) async {
            await db.execute('PRAGMA foreign_keys = ON');
          },
        ),
      );
    });

    tearDown(() async {
      await testDb.close();
    });

    test('Database creates tables correctly', () async {
      // Check if tables exist
      final tables = await testDb.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'",
      );
      
      final tableNames = tables.map((table) => table['name'] as String).toList();
      
      expect(tableNames, contains('cultures'));
      expect(tableNames, contains('recipes'));
    });

    test('Can insert and retrieve culture data', () async {
      // Insert test culture
      final cultureData = {
        'species': 'Arabidopsis thaliana',
        'variety': 'Columbia',
        'creation_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'current_stage': 'initiation',
        'status': 'active',
      };

      final cultureId = await testDb.insert('cultures', cultureData);
      expect(cultureId, greaterThan(0));

      // Retrieve the culture
      final cultures = await testDb.query(
        'cultures',
        where: 'id = ?',
        whereArgs: [cultureId],
      );

      expect(cultures.length, equals(1));
      expect(cultures.first['species'], equals('Arabidopsis thaliana'));
      expect(cultures.first['current_stage'], equals('initiation'));
      expect(cultures.first['status'], equals('active'));
    });

    test('Check constraints work correctly', () async {
      // Try to insert culture with invalid stage (should fail)
      try {
        await testDb.insert('cultures', {
          'species': 'Test Species',
          'creation_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
          'current_stage': 'invalid_stage', // Invalid stage
          'status': 'active',
        });
        fail('Should have thrown check constraint error');
      } catch (e) {
        expect(e.toString(), contains('CHECK constraint failed'));
      }
    });

    test('Recipe templates are accessible', () async {
      // Check if recipe template was inserted
      final templates = await testDb.query(
        'recipes',
        where: 'is_template = ?',
        whereArgs: [1],
      );
      
      expect(templates.length, greaterThan(0));
      expect(templates.first['name'], equals('MS Medium'));
    });
  });
}
