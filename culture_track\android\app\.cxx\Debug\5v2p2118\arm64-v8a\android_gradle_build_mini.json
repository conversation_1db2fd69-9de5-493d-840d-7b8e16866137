{"buildFiles": ["D:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\CultureTrack-augment\\culture_track\\android\\app\\.cxx\\Debug\\5v2p2118\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\CultureTrack-augment\\culture_track\\android\\app\\.cxx\\Debug\\5v2p2118\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}