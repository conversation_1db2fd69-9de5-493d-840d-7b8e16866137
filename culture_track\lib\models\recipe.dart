import 'dart:convert';

/// Represents a media recipe in the system
/// Maps to the recipes table in the database
class Recipe {
  final int? id;
  final String name;
  final String? description;
  final String mediaType;
  final Map<String, Ingredient> ingredients;
  final String? instructions;
  final String? source;
  final bool isTemplate;
  final DateTime createdDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  Recipe({
    this.id,
    required this.name,
    this.description,
    required this.mediaType,
    required this.ingredients,
    this.instructions,
    this.source,
    this.isTemplate = false,
    DateTime? createdDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdDate = createdDate ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// Create Recipe from database map
  factory Recipe.fromMap(Map<String, dynamic> map) {
    return Recipe(
      id: map['id'] as int?,
      name: map['name'] as String,
      description: map['description'] as String?,
      mediaType: map['media_type'] as String,
      ingredients: _parseIngredients(map['ingredients_json'] as String),
      instructions: map['instructions'] as String?,
      source: map['source'] as String?,
      isTemplate: (map['is_template'] as int) == 1,
      createdDate: DateTime.fromMillisecondsSinceEpoch(
        (map['created_date'] as int) * 1000,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        (map['created_at'] as int) * 1000,
      ),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(
        (map['updated_at'] as int) * 1000,
      ),
    );
  }

  /// Convert Recipe to database map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'description': description,
      'media_type': mediaType,
      'ingredients_json': _encodeIngredients(ingredients),
      'instructions': instructions,
      'source': source,
      'is_template': isTemplate ? 1 : 0,
      'created_date': createdDate.millisecondsSinceEpoch ~/ 1000,
      'created_at': createdAt.millisecondsSinceEpoch ~/ 1000,
      'updated_at': updatedAt.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// Create a copy of Recipe with updated fields
  Recipe copyWith({
    int? id,
    String? name,
    String? description,
    String? mediaType,
    Map<String, Ingredient>? ingredients,
    String? instructions,
    String? source,
    bool? isTemplate,
    DateTime? createdDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipe(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      mediaType: mediaType ?? this.mediaType,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      source: source ?? this.source,
      isTemplate: isTemplate ?? this.isTemplate,
      createdDate: createdDate ?? this.createdDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Parse ingredients JSON string to `Map<String, Ingredient>`
  static Map<String, Ingredient> _parseIngredients(String ingredientsJson) {
    try {
      final Map<String, dynamic> ingredientsMap = jsonDecode(ingredientsJson);
      final Map<String, Ingredient> result = {};

      ingredientsMap.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          result[key] = Ingredient.fromMap(value);
        }
      });

      return result;
    } catch (e) {
      return {};
    }
  }

  /// Encode ingredients `Map<String, Ingredient>` to JSON string
  static String _encodeIngredients(Map<String, Ingredient> ingredients) {
    final Map<String, dynamic> ingredientsMap = {};
    ingredients.forEach((key, ingredient) {
      ingredientsMap[key] = ingredient.toMap();
    });
    return jsonEncode(ingredientsMap);
  }

  /// Get total ingredient count
  int get ingredientCount => ingredients.length;

  /// Check if recipe has all required ingredients
  bool get isComplete {
    return name.isNotEmpty && mediaType.isNotEmpty && ingredients.isNotEmpty;
  }

  @override
  String toString() {
    return 'Recipe(id: $id, name: $name, mediaType: $mediaType, isTemplate: $isTemplate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recipe && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Represents an ingredient in a recipe
class Ingredient {
  final double amount;
  final String unit;
  final String? notes;

  Ingredient({required this.amount, required this.unit, this.notes});

  factory Ingredient.fromMap(Map<String, dynamic> map) {
    return Ingredient(
      amount: (map['amount'] as num).toDouble(),
      unit: map['unit'] as String,
      notes: map['notes'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {'amount': amount, 'unit': unit, if (notes != null) 'notes': notes};
  }

  /// Get formatted amount with unit
  String get formattedAmount {
    if (amount == amount.toInt()) {
      return '${amount.toInt()} $unit';
    }
    return '$amount $unit';
  }

  @override
  String toString() {
    return 'Ingredient(amount: $amount, unit: $unit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Ingredient &&
        other.amount == amount &&
        other.unit == unit &&
        other.notes == notes;
  }

  @override
  int get hashCode => Object.hash(amount, unit, notes);
}

/// Common media types for tissue culture recipes
enum MediaType {
  initiation('initiation'),
  multiplication('multiplication'),
  rooting('rooting'),
  acclimatization('acclimatization'),
  maintenance('maintenance');

  const MediaType(this.value);
  final String value;

  static MediaType fromString(String value) {
    return MediaType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MediaType.initiation,
    );
  }

  String get displayName {
    switch (this) {
      case MediaType.initiation:
        return 'Initiation Medium';
      case MediaType.multiplication:
        return 'Multiplication Medium';
      case MediaType.rooting:
        return 'Rooting Medium';
      case MediaType.acclimatization:
        return 'Acclimatization Medium';
      case MediaType.maintenance:
        return 'Maintenance Medium';
    }
  }
}
