import 'package:flutter/material.dart';

class RecipeDetailScreen extends StatelessWidget {
  final String recipeId;

  const RecipeDetailScreen({
    super.key,
    required this.recipeId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Recipe Details'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: Navigate to edit recipe screen
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Edit functionality coming soon!'),
                ),
              );
            },
            tooltip: 'Edit Recipe',
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: () {
              // TODO: Navigate to recipe calculator
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Calculator functionality coming soon!'),
                ),
              );
            },
            tooltip: 'Calculate Quantities',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Recipe ID: $recipeId',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('Name: Sample Recipe'),
                    const Text('Type: MS Medium'),
                    const Text('Description: Basic MS medium for tissue culture'),
                    const Text('Created: Today'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ingredients',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('• MS Salts: 4.4g/L'),
                    Text('• Sucrose: 30g/L'),
                    Text('• Agar: 8g/L'),
                    Text('• pH: 5.8'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('1. Dissolve MS salts in distilled water'),
                    Text('2. Add sucrose and mix well'),
                    Text('3. Adjust pH to 5.8'),
                    Text('4. Add agar and autoclave'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
