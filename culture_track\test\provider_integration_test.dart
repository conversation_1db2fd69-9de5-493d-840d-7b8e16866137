import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:culture_track/main.dart';
import 'package:culture_track/providers/culture_provider.dart';
import 'package:culture_track/providers/recipe_provider.dart';

void main() {
  group('Provider Integration Tests', () {
    testWidgets('Providers should be accessible in widget tree', (
      WidgetTester tester,
    ) async {
      // Build the app
      await tester.pumpWidget(const CultureTrackApp());

      // Create a test widget that tries to access providers
      final testWidget = Builder(
        builder: (context) {
          // Access providers to verify they're available
          Provider.of<CultureProvider>(context, listen: false);
          Provider.of<RecipeProvider>(context, listen: false);

          return const Column(
            children: [
              Text('Culture Provider: Available'),
              Text('Recipe Provider: Available'),
            ],
          );
        },
      );

      // Wrap test widget with the same providers
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => CultureProvider()),
            ChangeNotifierProvider(create: (_) => RecipeProvider()),
          ],
          child: MaterialApp(home: Scaffold(body: testWidget)),
        ),
      );

      // Verify providers are accessible
      expect(find.text('Culture Provider: Available'), findsOneWidget);
      expect(find.text('Recipe Provider: Available'), findsOneWidget);
    });

    testWidgets('Providers should have correct initial state', (
      WidgetTester tester,
    ) async {
      late CultureProvider cultureProvider;
      late RecipeProvider recipeProvider;

      final testWidget = Builder(
        builder: (context) {
          cultureProvider = Provider.of<CultureProvider>(
            context,
            listen: false,
          );
          recipeProvider = Provider.of<RecipeProvider>(context, listen: false);

          return Column(
            children: [
              Text('Cultures: ${cultureProvider.cultures.length}'),
              Text('Recipes: ${recipeProvider.recipes.length}'),
              Text('Loading: ${cultureProvider.isLoading}'),
            ],
          );
        },
      );

      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => CultureProvider()),
            ChangeNotifierProvider(create: (_) => RecipeProvider()),
          ],
          child: MaterialApp(home: Scaffold(body: testWidget)),
        ),
      );

      // Verify initial state
      expect(find.text('Cultures: 0'), findsOneWidget);
      expect(find.text('Recipes: 0'), findsOneWidget);
      expect(find.text('Loading: false'), findsOneWidget);

      // Verify provider properties
      expect(cultureProvider.cultures, isEmpty);
      expect(cultureProvider.isLoading, false);
      expect(cultureProvider.error, null);

      expect(recipeProvider.recipes, isEmpty);
      expect(recipeProvider.isLoading, false);
      expect(recipeProvider.error, null);
    });

    testWidgets('Consumer widgets should rebuild on provider changes', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => CultureProvider()),
            ChangeNotifierProvider(create: (_) => RecipeProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: Consumer<CultureProvider>(
                builder: (context, provider, child) {
                  return Text(
                    'Active Cultures: ${provider.activeCulturesCount}',
                  );
                },
              ),
            ),
          ),
        ),
      );

      // Initial state
      expect(find.text('Active Cultures: 0'), findsOneWidget);

      // The consumer should be working and displaying the correct initial count
      // In a real test, we would trigger provider changes and verify UI updates
    });
  });
}
