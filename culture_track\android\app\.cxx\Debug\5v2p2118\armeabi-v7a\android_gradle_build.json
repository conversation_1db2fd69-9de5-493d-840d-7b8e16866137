{"buildFiles": ["D:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\CultureTrack-augment\\culture_track\\android\\app\\.cxx\\Debug\\5v2p2118\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\CultureTrack-augment\\culture_track\\android\\app\\.cxx\\Debug\\5v2p2118\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\src\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\src\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}