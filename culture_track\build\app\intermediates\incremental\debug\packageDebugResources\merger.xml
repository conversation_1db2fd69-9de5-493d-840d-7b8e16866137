<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res"><file name="launch_background" path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="launch_background" path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher" path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\GitHub\CultureTrack-augment\culture_track\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\GitHub\CultureTrack-augment\culture_track\build\app\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\GitHub\CultureTrack-augment\culture_track\build\app\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>